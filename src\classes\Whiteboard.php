<?php

declare(strict_types=1);

namespace App\classes;

use Exception;
use PDO;
use PDOException;

class Whiteboard
{
	// --- Atributos ---
	private ?int    $id          = null;
	private ?string $name        = null;
	private ?string $image_data  = null;
	private ?int    $width       = null;
	private ?int    $height      = null;
	private ?string $created_at  = null;

	/**
	 * Constructor: Inicializa las propiedades del objeto Whiteboard.
	 */
	public function __construct()
	{
		$this->id         = 0;
		$this->name       = null;
		$this->image_data = null;
		$this->width      = null;
		$this->height     = null;
		$this->created_at = null;
	}

	/**
	 * Método estático para construir un objeto Whiteboard desde un array (ej. fila de DB).
	 *
	 * @param array $resultado Array asociativo con los datos del whiteboard.
	 *
	 * @return self Instancia de Whiteboard.
	 * @throws Exception Si ocurre un error durante la construcción.
	 */
	public static function construct(array $resultado = []): self
	{
		try {
			$objeto              = new self();
			$objeto->id          = isset($resultado['id']) ? (int)$resultado['id'] : 0;
			$objeto->name        = $resultado['name'] ?? null;
			$objeto->image_data  = $resultado['image_data'] ?? null;
			$objeto->width       = isset($resultado['width']) ? (int)$resultado['width'] : null;
			$objeto->height      = isset($resultado['height']) ? (int)$resultado['height'] : null;
			$objeto->created_at  = $resultado['created_at'] ?? null;

			return $objeto;

		} catch (Exception $e) {
			throw new Exception("Error al construir objeto Whiteboard: " . $e->getMessage());
		}
	}

	/**
	 * Obtiene un whiteboard por su ID.
	 *
	 * @param int $id       ID del whiteboard.
	 * @param PDO $conexion Conexión PDO.
	 *
	 * @return self|null Objeto Whiteboard o null si no se encuentra.
	 * @throws Exception Si hay error en DB.
	 */
	public static function obtenerPorId(int $id, PDO $conexion): ?self
	{
		try {
			// Consulta para obtener whiteboard por ID (Usando Heredoc)
			$query = <<<SQL
            SELECT
            	*
            FROM whiteboards
            WHERE
            	id = :id
            LIMIT 1
            SQL;

			$statement = $conexion->prepare($query);
			$statement->bindValue(':id', $id, PDO::PARAM_INT);
			$statement->execute();
			$resultado = $statement->fetch(PDO::FETCH_ASSOC);

			return $resultado ? self::construct($resultado) : null;

		} catch (PDOException $e) {
			throw new Exception("Error al obtener Whiteboard por ID: " . $e->getMessage());
		}
	}

	/**
	 * Obtiene una lista de todos los whiteboards.
	 *
	 * @param PDO $conexion Conexión PDO.
	 *
	 * @return array Array de objetos Whiteboard.
	 * @throws Exception Si hay error en DB.
	 */
	public static function obtenerTodos(PDO $conexion): array
	{
		try {
			// Consulta para obtener lista de whiteboards (Usando Heredoc)
			$query = <<<SQL
            SELECT
            	*
            FROM whiteboards
            ORDER BY
            	created_at DESC, name ASC
            SQL;

			$statement = $conexion->prepare($query);
			$statement->execute();
			$resultados = $statement->fetchAll(PDO::FETCH_ASSOC);

			$listado = [];
			foreach ($resultados as $resultado) {
				$listado[] = self::construct($resultado);
			}
			return $listado;

		} catch (PDOException $e) {
			throw new Exception("Error al obtener lista de Whiteboards: " . $e->getMessage());
		}
	}

	/**
	 * Obtiene un whiteboard por su nombre.
	 *
	 * @param string $name     Nombre del whiteboard.
	 * @param PDO    $conexion Conexión PDO.
	 *
	 * @return self|null Objeto Whiteboard o null si no se encuentra.
	 * @throws Exception Si hay error en DB.
	 */
	public static function obtenerPorNombre(string $name, PDO $conexion): ?self
	{
		try {
			// Consulta para obtener whiteboard por nombre (Usando Heredoc)
			$query = <<<SQL
            SELECT
            	*
            FROM whiteboards
            WHERE
            	name = :name
            LIMIT 1
            SQL;

			$statement = $conexion->prepare($query);
			$statement->bindValue(':name', $name, PDO::PARAM_STR);
			$statement->execute();
			$resultado = $statement->fetch(PDO::FETCH_ASSOC);

			return $resultado ? self::construct($resultado) : null;

		} catch (PDOException $e) {
			throw new Exception("Error al obtener Whiteboard por nombre: " . $e->getMessage());
		}
	}

	/**
	 * Crea un nuevo whiteboard en la base de datos a partir de un objeto Whiteboard.
	 *
	 * @param PDO $conexion Conexión PDO.
	 *
	 * @return int|false El ID del nuevo whiteboard creado o false en caso de error.
	 * @throws Exception Si los datos requeridos en el objeto están vacíos o hay error en DB.
	 */
	function crear(PDO $conexion): int|false
	{
		// Validaciones básicas sobre el objeto
		if (empty(trim($this->getName())) || empty($this->getImage_data()) || 
			$this->getWidth() === null || $this->getHeight() === null) {
			throw new Exception("Nombre, datos de imagen, ancho y alto son requeridos en el objeto Whiteboard para crearlo.");
		}

		// Validar nombre no vacío
		if (!$this->validarNombre($this->getName())) {
			throw new Exception("El nombre no puede estar vacío.");
		}

		// Validar datos de imagen no vacíos
		if (!$this->validarImageData($this->getImage_data())) {
			throw new Exception("Los datos de imagen no pueden estar vacíos.");
		}

		// Validar dimensiones positivas
		if (!$this->validarDimensionPositiva($this->getWidth()) || 
			!$this->validarDimensionPositiva($this->getHeight())) {
			throw new Exception("El ancho y alto deben ser números enteros positivos.");
		}

		try {
			return $this->_insert($conexion);
		} catch (Exception $e) {
			throw new Exception("Error al crear whiteboard: " . $e->getMessage());
		}
	}

	/**
	 * Modifica un whiteboard existente.
	 *
	 * @param PDO $conexion Conexión PDO.
	 *
	 * @return bool True si la modificación fue exitosa, False en caso contrario.
	 * @throws Exception Si los datos requeridos están vacíos o si ocurre un error de base de datos.
	 */
	function modificar(PDO $conexion): bool
	{
		// Validar que el ID sea válido
		if ($this->getId() <= 0) {
			throw new Exception("ID de whiteboard no válido para modificar.");
		}

		// Validar nombre no vacío
		if (!$this->validarNombre($this->getName())) {
			throw new Exception("El nombre no puede estar vacío.");
		}

		// Validar datos de imagen no vacíos
		if (!$this->validarImageData($this->getImage_data())) {
			throw new Exception("Los datos de imagen no pueden estar vacíos.");
		}

		// Validar dimensiones positivas
		if (!$this->validarDimensionPositiva($this->getWidth()) || 
			!$this->validarDimensionPositiva($this->getHeight())) {
			throw new Exception("El ancho y alto deben ser números enteros positivos.");
		}

		try {
			return $this->_update($conexion);
		} catch (Exception $e) {
			throw new Exception("Error al modificar whiteboard: " . $e->getMessage());
		}
	}

	/**
	 * Método privado para insertar un nuevo whiteboard en la base de datos.
	 *
	 * @param PDO $conexion Conexión PDO.
	 *
	 * @return int|false El ID del nuevo whiteboard creado o false en caso de error.
	 * @throws Exception Si hay error en DB.
	 */
	private function _insert(PDO $conexion): int|false
	{
		try {
			// Establecer la zona horaria para Colombia
			date_default_timezone_set('America/Bogota');

			// Preparar la consulta INSERT usando Heredoc
			$query = <<<SQL
            INSERT INTO whiteboards (
            	 name
            	,image_data
            	,width
            	,height
            ) VALUES (
            	 :name
            	,:image_data
            	,:width
            	,:height
            )
            SQL;

			$statement = $conexion->prepare($query);

			// Bind de parámetros desde el objeto
			$statement->bindValue(':name', trim($this->getName()), PDO::PARAM_STR);
			$statement->bindValue(':image_data', $this->getImage_data(), PDO::PARAM_STR);
			$statement->bindValue(':width', $this->getWidth(), PDO::PARAM_INT);
			$statement->bindValue(':height', $this->getHeight(), PDO::PARAM_INT);

			// Ejecutar la consulta
			$success = $statement->execute();

			if ($success) {
				// Devolver el ID del whiteboard recién creado
				return (int)$conexion->lastInsertId();
			} else {
				return false; // Error en la ejecución
			}

		} catch (PDOException $e) {
			// Manejar errores específicos de DB (ej. nombre duplicado)
			if ($e->getCode() == 23000 || $e->getCode() == 1062) { // Códigos comunes para violación de UNIQUE constraint
				throw new Exception("Error al crear whiteboard: El nombre '{$this->getName()}' ya existe.");
			} else {
				throw new Exception("Error de base de datos al crear whiteboard: " . $e->getMessage());
			}
		}
	}

	/**
	 * Método privado para actualizar un whiteboard existente en la base de datos.
	 *
	 * @param PDO $conexion Conexión PDO.
	 *
	 * @return bool True si la actualización fue exitosa, False en caso contrario.
	 * @throws Exception Si hay error en DB.
	 */
	private function _update(PDO $conexion): bool
	{
		try {
			// Consulta para actualizar el whiteboard
			$query = <<<SQL
            UPDATE whiteboards SET
                name = :name,
                image_data = :image_data,
                width = :width,
                height = :height
            WHERE
                id = :id
            SQL;

			$statement = $conexion->prepare($query);
			$statement->bindValue(':name', trim($this->getName()), PDO::PARAM_STR);
			$statement->bindValue(':image_data', $this->getImage_data(), PDO::PARAM_STR);
			$statement->bindValue(':width', $this->getWidth(), PDO::PARAM_INT);
			$statement->bindValue(':height', $this->getHeight(), PDO::PARAM_INT);
			$statement->bindValue(':id', $this->getId(), PDO::PARAM_INT);

			// execute() devuelve true en éxito, false en error.
			return $statement->execute();

		} catch (PDOException $e) {
			// Manejar errores específicos de DB (ej. nombre duplicado)
			if ($e->getCode() == 23000 || $e->getCode() == 1062) { // Códigos comunes para violación de UNIQUE constraint
				throw new Exception("Error al modificar whiteboard: El nombre '{$this->getName()}' ya existe.");
			} else {
				throw new Exception("Error de base de datos al modificar whiteboard (ID: {$this->getId()}): " . $e->getMessage());
			}
		}
	}

	/**
	 * Elimina un whiteboard por su ID.
	 *
	 * @param int $id       ID del whiteboard a eliminar.
	 * @param PDO $conexion Conexión PDO.
	 *
	 * @return bool True si la eliminación fue exitosa, False en caso contrario.
	 * @throws Exception Si hay error en DB.
	 */
	public static function eliminar(int $id, PDO $conexion): bool
	{
		try {
			// Consulta para eliminar whiteboard por ID
			$query = <<<SQL
            DELETE FROM whiteboards
            WHERE
                id = :id
            SQL;

			$statement = $conexion->prepare($query);
			$statement->bindValue(':id', $id, PDO::PARAM_INT);

			return $statement->execute();

		} catch (PDOException $e) {
			throw new Exception("Error de base de datos al eliminar whiteboard (ID: $id): " . $e->getMessage());
		}
	}

	// --- Métodos de validación ---

	/**
	 * Valida que el nombre no esté vacío.
	 *
	 * @param string|null $nombre Nombre a validar.
	 *
	 * @return bool True si es válido, False en caso contrario.
	 */
	private function validarNombre(?string $nombre): bool
	{
		return !empty(trim($nombre ?? ''));
	}

	/**
	 * Valida que los datos de imagen no estén vacíos.
	 *
	 * @param string|null $imageData Datos de imagen a validar.
	 *
	 * @return bool True si es válido, False en caso contrario.
	 */
	private function validarImageData(?string $imageData): bool
	{
		return !empty($imageData);
	}

	/**
	 * Valida que una dimensión sea un entero positivo.
	 *
	 * @param int|null $dimension Dimensión a validar.
	 *
	 * @return bool True si es válido, False en caso contrario.
	 */
	private function validarDimensionPositiva(?int $dimension): bool
	{
		return $dimension !== null && is_int($dimension) && $dimension > 0;
	}

	// --- Getters y Setters ---

	public function getId(): ?int
	{
		return $this->id;
	}

	public function setId(?int $id): self
	{
		$this->id = $id;
		return $this;
	}

	public function getName(): ?string
	{
		return $this->name;
	}

	public function setName(?string $name): self
	{
		$this->name = $name;
		return $this;
	}

	public function getImage_data(): ?string
	{
		return $this->image_data;
	}

	public function setImage_data(?string $image_data): self
	{
		$this->image_data = $image_data;
		return $this;
	}

	public function getWidth(): ?int
	{
		return $this->width;
	}

	public function setWidth(?int $width): self
	{
		$this->width = $width;
		return $this;
	}

	public function getHeight(): ?int
	{
		return $this->height;
	}

	public function setHeight(?int $height): self
	{
		$this->height = $height;
		return $this;
	}

	public function getCreated_at(): ?string
	{
		return $this->created_at;
	}

	public function setCreated_at(?string $created_at): self
	{
		$this->created_at = $created_at;
		return $this;
	}
}
